#include <stdio.h>
#include <dlfcn.h>
#include <link.h>
#include <apr_pools.h>
#include <httpd.h>
#include <http_request.h>
#include <http_config.h>

#include "library.h"

void* GetModuleBaseByDlopen(const char* module_name) {
    // 尝试获取已加载模块的句柄
    void* handle = dlopen(module_name, RTLD_NOW | RTLD_NOLOAD);
    if (!handle) {
        return NULL;
    }

    // 获取模块信息
    struct link_map* map;
    if (dlinfo(handle, RTLD_DI_LINKMAP, &map) == 0) {
        void* base_addr = (void*)map->l_addr;
        dlclose(handle);
        return base_addr;
    }

    dlclose(handle);
    return NULL;
}

static int check_authn_hook(request_rec *r) {
    static int initialized = 0;
    if (!initialized) {
        //InitHook();
        //InitHook2();
        initialized = 1;
    }

    return DECLINED;
}

static void register_hooks(apr_pool_t* pool)
{
    ap_hook_check_authn(check_authn_hook, NULL, NULL,
                        APR_HOOK_FIRST,
                        AP_AUTH_INTERNAL_PER_CONF);
}

/* 模块声明 */
module AP_MODULE_DECLARE_DATA authn_base_module = {
        STANDARD20_MODULE_STUFF,
        NULL,                   /* create per-dir config */
        NULL,                   /* merge per-dir config */
        NULL,                   /* server config */
        NULL,                   /* merge server config */
        NULL,                   /* command table */
        register_hooks         /* register hooks */
};